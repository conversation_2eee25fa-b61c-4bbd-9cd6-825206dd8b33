import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:flutter/services.dart';

class VideoCreationParams {
  final String audioFile;
  final String userImageFile;
  final String borderFile;
  final String brandLogoFile;
  final String lyricsFile;
  final String waveformVideo;
  final String trackName;
  final String username;
  final String originalArtist;
  final String outputPath;
  final String fontPath;
  final String arialFontPath;

  VideoCreationParams({
    required this.audioFile,
    required this.userImageFile,
    required this.borderFile,
    required this.brandLogoFile,
    required this.lyricsFile,
    required this.waveformVideo,
    required this.trackName,
    required this.username,
    required this.originalArtist,
    required this.outputPath,
    required this.fontPath,
    required this.arialFontPath,
  });
}

class CanvasVideoCreator {
  // Video dimensions and layout constants
  static const int bgWidth = 720;
  static const int bgHeight = 1360;
  static const int imageSize = 240;
  static const int logoSize = 120;
  static const int blockHeight = 120;
  static const int lineHeightCurrent = 46;
  static const int lineHeightOther = 36;
  static const int fontSizeCurrent = 42;
  static const int fontSizeOther = 24;

  // Colors (ARGB format)
  static const String colorCurrentHex = "#ECAED9DE";
  static const String colorOtherHex = "#ECAED961";
  static const String colorMetaHex = "#FEFEFECC";

  /// Main entry point for creating lyric video
  static Future<void> createLyricVideo({
    required VideoCreationParams params,
  }) async {
    try {
      log("Starting simplified canvas-based video creation...");

      // Parse lyrics data
      final lyricsData = await _parseLyricsFile(params.lyricsFile);
      final durationSeconds = await _getAudioDuration(params.audioFile);

      log("Audio duration: ${durationSeconds}s, Lyrics count: ${lyricsData.length}");

      // Calculate layout positions
      final layout = _calculateLayout();

      // Create a simple FFmpeg command without complex canvas rendering for now
      await _executeSimpleFFmpegCommand(
        params: params,
        layout: layout,
        durationSeconds: durationSeconds,
      );

      log("Video creation completed successfully!");
    } catch (e, stackTrace) {
      log("Error in video creation: $e");
      log("Stack trace: $stackTrace");
      rethrow;
    }
  }

  // Helper methods

  /// Parse lyrics file and return structured data
  static Future<List<dynamic>> _parseLyricsFile(String lyricsFile) async {
    final jsonContent = await File(lyricsFile).readAsString();
    final Map<String, dynamic> lyricsJson = json.decode(jsonContent);
    return (lyricsJson["lyrics"]["data"] ?? []) as List<dynamic>;
  }

  /// Calculate layout positions based on video dimensions
  static _VideoLayout _calculateLayout() {
    final userX = ((bgWidth - imageSize) / 6).floor();
    final userY = (bgHeight * 0.15 - imageSize / 2).floor();
    final logoX = userX + imageSize - (logoSize ~/ 2) - 40;
    final logoY = userY + imageSize - (logoSize ~/ 2) - 20;
    final textX = (bgWidth - imageSize) / 1.7;
    final textY = userY + 40.0;
    final waveformHeight = (bgHeight * 0.22).floor();
    final waveformY = ((bgHeight - waveformHeight) / 2.25).floor();
    final lyricsStartY = waveformY + waveformHeight + 250;

    return _VideoLayout(
      userX: userX,
      userY: userY,
      logoX: logoX,
      logoY: logoY,
      textX: textX,
      textY: textY,
      waveformHeight: waveformHeight,
      waveformY: waveformY,
      lyricsStartY: lyricsStartY,
    );
  }

  /// Create temporary directory for image rendering
  static Future<Directory> _createTempDirectory() async {
    final tempDir = Directory("${Directory.systemTemp.path}/melodyze_canvas_${DateTime.now().millisecondsSinceEpoch}");
    if (!await tempDir.exists()) {
      await tempDir.create(recursive: true);
    }
    return tempDir;
  }

  /// Register font family from file path
  static Future<String> _registerFont(String fontPath) async {
    final fontFamily = "Font${_generateHash(fontPath)}";
    final loader = FontLoader(fontFamily);
    final bytes = await File(fontPath).readAsBytes();
    loader.addFont(Future.value(bytes.buffer.asByteData()));
    await loader.load();
    return fontFamily;
  }

  /// Generate simple hash for font family names
  static String _generateHash(String input) {
    int hash = 0;
    for (int i = 0; i < input.length; i++) {
      hash = ((hash << 5) - hash + input.codeUnitAt(i)) & 0xffffffff;
    }
    return hash.abs().toString();
  }

  /// Parse time string to seconds
  static double _parseTime(String timeString) {
    final parts = timeString.split(":");
    if (parts.length != 3) return 0.0;
    return int.parse(parts[0]) * 60 + int.parse(parts[1]) + (int.parse(parts[2]) / 1000.0);
  }

  /// Wrap text into multiple lines
  static List<String> _wrapText(String text, {int maxChars = 25}) {
    final words = text.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).toList();
    final lines = <String>[];
    var current = "";
    for (final word in words) {
      if (current.isEmpty) {
        current = word;
      } else if ((current.length + 1 + word.length) <= maxChars) {
        current = "$current $word";
      } else {
        lines.add(current);
        current = word;
      }
    }
    if (current.isNotEmpty) lines.add(current);
    return lines;
  }

  /// Parse color from hex string
  static ui.Color _parseColor(String hex) {
    String h = hex.replaceAll("#", "");
    if (h.length == 8) {
      final a = int.parse(h.substring(0, 2), radix: 16);
      final r = int.parse(h.substring(2, 4), radix: 16);
      final g = int.parse(h.substring(4, 6), radix: 16);
      final b = int.parse(h.substring(6, 8), radix: 16);
      return ui.Color.fromARGB(a, r, g, b);
    }
    if (h.length == 6) {
      final r = int.parse(h.substring(0, 2), radix: 16);
      final g = int.parse(h.substring(2, 4), radix: 16);
      final b = int.parse(h.substring(4, 6), radix: 16);
      return ui.Color.fromARGB(0xFF, r, g, b);
    }
    return const ui.Color(0xFFFFFFFF);
  }

  /// Get audio duration using FFmpeg probe
  static Future<double> _getAudioDuration(String audioPath) async {
    final cmd = "-i '$audioPath' -hide_banner";
    final session = await FFmpegKit.execute(cmd);
    final logs = await session.getAllLogsAsString() ?? "";
    final match = RegExp(r"Duration:\s*(\d+):(\d+):(\d+\.\d+)").firstMatch(logs);
    if (match != null) {
      final h = int.parse(match.group(1)!);
      final m = int.parse(match.group(2)!);
      final s = double.parse(match.group(3)!);
      return h * 3600 + m * 60 + s;
    }
    return 180.0; // Fallback duration
  }

  /// Generate lyric timing specifications
  static List<_LyricSpec> _generateLyricSpecs(List<dynamic> lyricsData, double durationSeconds) {
    final specs = <_LyricSpec>[];
    if (lyricsData.isEmpty) return specs;

    // Add preroll if first lyric doesn't start at 0
    final first = lyricsData.first;
    final firstStart = _parseTime(first["start_time"] ?? "0:0:0");
    if (firstStart > 0) {
      final currentLines = _wrapText(first["text"] ?? "", maxChars: 20);
      specs.add(_LyricSpec(
        start: 0.0,
        end: firstStart,
        currentLines: currentLines,
        prevLines: const [],
        nextLines: lyricsData.length > 1 ? _wrapText(lyricsData[1]["text"] ?? "", maxChars: 25) : const [],
        isPreroll: true,
      ));
    }

    // Add main lyric segments
    for (int i = 0; i < lyricsData.length; i++) {
      final item = lyricsData[i];
      final start = _parseTime(item["start_time"] ?? "0:0:0");
      double end = _parseTime(item["end_time"] ?? "0:0:0");
      if (end == 0.0 && i == lyricsData.length - 1) {
        end = durationSeconds;
      }

      final currentLines = _wrapText(item["text"] ?? "", maxChars: 20);
      final prevLines = i > 0 ? _wrapText(lyricsData[i - 1]["text"] ?? "", maxChars: 25) : <String>[];
      final nextLines = i < lyricsData.length - 1 ? _wrapText(lyricsData[i + 1]["text"] ?? "", maxChars: 25) : <String>[];

      specs.add(_LyricSpec(
        start: start,
        end: math.max(start, end),
        currentLines: currentLines,
        prevLines: prevLines,
        nextLines: nextLines,
      ));
    }

    return specs;
  }

  /// Execute simplified FFmpeg command without drawtext
  static Future<void> _executeSimpleFFmpegCommand({
    required VideoCreationParams params,
    required _VideoLayout layout,
    required double durationSeconds,
  }) async {
    // Build basic video composition without text overlays
    final List<String> filterComplex = [];

    // Create black background
    filterComplex.add("color=black:s=${bgWidth}x${bgHeight}:d=${durationSeconds.toStringAsFixed(3)}[bg]");

    // Scale and create circular user image
    filterComplex.add(
        "[1:v]scale=${imageSize}:${imageSize},format=yuva444p,geq=lum='p(X,Y)':a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'[usercircle]");

    // Scale border
    filterComplex.add("[2:v]scale=${imageSize + 18}:${imageSize + 18}[border]");

    // Overlay border on background
    filterComplex.add("[bg][border]overlay=x=${layout.userX - 10}:y=${layout.userY - 10}[step0]");

    // Overlay user image
    filterComplex.add("[step0][usercircle]overlay=x=${layout.userX}:y=${layout.userY}[step1]");

    // Scale and overlay logo
    filterComplex.add("[3:v]scale=${logoSize}:${logoSize}[logo]");
    filterComplex.add("[step1][logo]overlay=x=${layout.logoX}:y=${layout.logoY}[step2]");

    // Scale and overlay waveform
    filterComplex.add("[4:v]scale=${bgWidth}:${layout.waveformHeight}[waveform_scaled]");
    filterComplex.add("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]");
    filterComplex.add("[step2][waveform]overlay=x=0:y=${layout.waveformY}:repeatlast=0[final]");

    final filterComplexArg = filterComplex.join(";");

    final cmd = [
      "-y",
      "-f",
      "lavfi",
      "-i",
      "anullsrc=channel_layout=stereo:sample_rate=48000",
      "-i",
      params.userImageFile,
      "-i",
      params.borderFile,
      "-i",
      params.brandLogoFile,
      "-i",
      params.waveformVideo,
      "-i",
      params.audioFile,
      "-filter_complex",
      filterComplexArg,
      "-map",
      "[final]",
      "-map",
      "5:a",
      "-c:v",
      "libx264",
      "-preset",
      "veryfast",
      "-crf",
      "23",
      "-pix_fmt",
      "yuv420p",
      "-c:a",
      "aac",
      "-b:a",
      "128k",
      params.outputPath,
    ];

    log("Running simplified FFmpeg command: ${cmd.join(' ')}");

    final session = await FFmpegKit.executeWithArguments(cmd);
    final returnCode = await session.getReturnCode();
    final output = await session.getOutput();
    final failStack = await session.getFailStackTrace();

    log("Return Code: $returnCode");
    log("FFmpeg Output: $output");
    if (failStack != null) log("FFmpeg Stacktrace: $failStack");

    if (!(returnCode?.isValueSuccess() ?? false)) {
      throw Exception("FFmpeg failed with return code $returnCode");
    }
  }
}

// Data classes
class _VideoLayout {
  final int userX, userY, logoX, logoY;
  final double textX, textY;
  final int waveformHeight, waveformY, lyricsStartY;

  _VideoLayout({
    required this.userX,
    required this.userY,
    required this.logoX,
    required this.logoY,
    required this.textX,
    required this.textY,
    required this.waveformHeight,
    required this.waveformY,
    required this.lyricsStartY,
  });
}

class _LyricSpec {
  final double start, end;
  final List<String> currentLines, prevLines, nextLines;
  final bool isPreroll;

  _LyricSpec({
    required this.start,
    required this.end,
    required this.currentLines,
    required this.prevLines,
    required this.nextLines,
    this.isPreroll = false,
  });
}
