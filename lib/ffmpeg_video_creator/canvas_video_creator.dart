// current_code_updated.dart
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:flutter/services.dart';

class VideoCreationParams {
  final String audioFile;
  final String userImageFile;
  final String borderFile;
  final String brandLogoFile;
  final String lyricsFile;
  final String waveformVideo;
  final String trackName;
  final String username;
  final String originalArtist;
  final String outputPath;
  final String fontPath; // main display font (lyrics + trackName)
  final String arialFontPath; // metadata font (artist/username)

  VideoCreationParams({
    required this.audioFile,
    required this.userImageFile,
    required this.borderFile,
    required this.brandLogoFile,
    required this.lyricsFile,
    required this.waveformVideo,
    required this.trackName,
    required this.username,
    required this.originalArtist,
    required this.outputPath,
    required this.fontPath,
    required this.arialFontPath,
  });
}

class CanvasVideoCreator {
  // ---------- GEOMETRY / STYLE (mirrors your values) ----------
  static const int BG_W = 720;
  static const int BG_H = 1360;
  static const int IMG_SIZE = 240;
  static const int LOGO_SIZE = 120;

  static const int blockHeight = 120;
  static const int lineHeightCurrent = 46;
  static const int lineHeightOther = 36;
  static const int fontSizeCurrent = 42;
  static const int fontSizeOther = 24;

  // ARGB hex (from your #ECAED9DE, #ECAED961, #FEFEFECC)
  static const String colorCurrentHex = "#ECAED9DE";
  static const String colorOtherHex = "#ECAED961";
  static const String colorMetaHex = "#FEFEFECC";

  // ---------- PUBLIC ENTRY ----------
  static Future<void> createLyricVideo({
    required VideoCreationParams params,
  }) async {
    // Read lyric json
    final jsonContent = await File(params.lyricsFile).readAsString();
    final Map<String, dynamic> lyricsJson = json.decode(jsonContent);
    final List<dynamic> lyricsData = (lyricsJson["lyrics"]["data"] ?? []) as List<dynamic>;

    // Parse audio duration
    final durationSeconds = await _getAudioDuration(params.audioFile);

    // Precompute layout constants (same as your code)
    final int userX = ((BG_W - IMG_SIZE) / 6).floor();
    final int userY = (BG_H * 0.15 - IMG_SIZE / 2).floor();

    final int logoX = userX + IMG_SIZE - (LOGO_SIZE ~/ 2) - 40;
    final int logoY = userY + IMG_SIZE - (LOGO_SIZE ~/ 2) - 20;

    final double textX = (BG_W - IMG_SIZE) / 1.7;
    final double textY = userY + 40.0;

    final int waveformHeight = (BG_H * 0.22).floor();
    final int waveformY = ((BG_H - waveformHeight) / 2.25).floor();
    final int lyricsStartY = waveformY + waveformHeight + 250;

    // Prepare temp dir for generated PNGs
    final tempDir = await _prepareTempDir();
    final metaPath = "${tempDir.path}/metadata.png";

    // Load/register fonts once
    final String mainFamily = "Main${_shortHash(params.fontPath)}";
    final String metaFamily = "Meta${_shortHash(params.arialFontPath)}";
    await _registerFontFamilyFromFile(mainFamily, params.fontPath);
    await _registerFontFamilyFromFile(metaFamily, params.arialFontPath);

    // Render metadata static overlay (trackName, originalArtist, username)
    await _renderMetadataOverlay(
      outPath: metaPath,
      trackName: params.trackName,
      originalArtist: params.originalArtist.toUpperCase(),
      username: params.username,
      familyMain: mainFamily,
      familyMeta: metaFamily,
      colorHex: colorMetaHex,
      textX: textX,
      textY: textY,
    );

    // Render lyric state overlays
    final List<_LyricImageSpec> lyricSpecs = _buildLyricSpecs(
      lyricsData: lyricsData,
      durationSeconds: durationSeconds,
    );

    final List<String> lyricImagePaths = [];
    for (int i = 0; i < lyricSpecs.length; i++) {
      final spec = lyricSpecs[i];
      final path = "${tempDir.path}/lyric_${i.toString().padLeft(3, '0')}.png";
      await _renderLyricStateImage(
        outPath: path,
        spec: spec,
        familyMain: mainFamily,
        colorCurrentHex: colorCurrentHex,
        colorOtherHex: colorOtherHex,
        lyricsStartY: lyricsStartY,
      );
      lyricImagePaths.add(path);
    }

    // Build FFmpeg command: inputs
    // 0: anullsrc (bg audio hack for graph start) – retained from your flow
    // 1: userImage, 2: border, 3: brandLogo, 4: waveform, 5: audio
    // + static meta image
    // + each lyric overlay image (-loop 1 each, to act as stills)
    final inputs = <String>[
      "-f",
      "lavfi",
      "-i",
      "anullsrc=channel_layout=stereo:sample_rate=48000",
      "-i",
      params.userImageFile,
      "-i",
      params.borderFile,
      "-i",
      params.brandLogoFile,
      "-i",
      params.waveformVideo,
      "-i",
      params.audioFile,
      "-loop",
      "1",
      "-i",
      metaPath,
      for (final p in lyricImagePaths) ...["-loop", "1", "-i", p],
    ];

    // indices helpers
    const idxUser = 1;
    const idxBorder = 2;
    const idxLogo = 3;
    const idxWave = 4;
    const idxAudio = 5;
    const idxMeta = 6;
    const idxFirstLyric = 7; // onwards

    // Filter graph up to waveform (as in your original)
    final List<String> fc = [];
    fc.add("color=black:s=${BG_W}x${BG_H}:d=${durationSeconds.toStringAsFixed(3)}[bg]");
    fc.add("[${idxUser}:v]scale=${IMG_SIZE}:${IMG_SIZE},format=yuva444p,geq=lum='p(X,Y)':a='if(lte(pow(X-(W/2),2)+pow(Y-(H/2),2),pow(min(W,H)/2,2)),255,0)'[usercircle]");
    fc.add("[${idxBorder}:v]scale=${IMG_SIZE + 18}:${IMG_SIZE + 18}[border]");
    fc.add("[bg][border]overlay=x=${userX - 10}:y=${userY - 10}[step0]");
    fc.add("[step0][usercircle]overlay=x=${userX}:y=${userY}[step1]");
    fc.add("[${idxLogo}:v]scale=${LOGO_SIZE}:${LOGO_SIZE}[logo]");
    fc.add("[step1][logo]overlay=x=${logoX}:y=${logoY}[step2]");
    fc.add("[${idxWave}:v]scale=${BG_W}:${waveformHeight}[waveform_scaled]");
    fc.add("[waveform_scaled]loop=loop=-1:size=32767:start=0[waveform]");
    fc.add("[step2][waveform]overlay=x=0:y=${waveformY}:repeatlast=0[base]");
    fc.add("[base][${idxMeta}:v]overlay=x=0:y=0[base_meta]");

    // Overlay each lyric image with enable between(t,start,end)
    String lastLabel = "base_meta";
    for (int i = 0; i < lyricSpecs.length; i++) {
      final spec = lyricSpecs[i];
      final inIdx = idxFirstLyric + i;
      final outLabel = "base_${i}";
      // Full-frame lyric PNGs already positioned, so x=0:y=0
      fc.add("[$lastLabel][${inIdx}:v]overlay=x=0:y=0:enable='between(t,${_fmt(spec.start)},${_fmt(spec.end)})'[$outLabel]");

      lastLabel = outLabel;
    }

    final String filterComplexArg = fc.join(";");

    // Encoding (fast) – tune as needed. You can also pick hardware codecs conditionally per platform.
    final cmd = [
      "-y",
      ...inputs,
      "-filter_complex", filterComplexArg,
      "-map", "[$lastLabel]",
      "-map", "$idxAudio:a",
      "-c:v", "libx264",
      "-preset", "veryfast", // faster than medium
      "-crf", "23",
      "-pix_fmt", "yuv420p",
      "-c:a", "aac",
      "-b:a", "128k",
      params.outputPath,
    ];

    final fullCmd = cmd.join(" ");
    log("Running FFmpeg command:\n$fullCmd");

    final session = await FFmpegKit.executeWithArguments(cmd);
    final returnCode = await session.getReturnCode();

    // final logs = await session.getAllLogsAsString();
    final output = await session.getOutput();
    final failStack = await session.getFailStackTrace();

    log("Return Code: $returnCode");
    log("FFmpeg Output:\n$output");
    // log("FFmpeg Logs:\n$logs")
    log("FFmpeg Stacktrace:\n$failStack");

    // Decide if you want to throw on non-success codes
    // if (!(returnCode?.isValueSuccess() ?? false)) {
    //   throw Exception("FFmpeg failed with $returnCode");
    // }
  }

  // ---------- Helpers: Specs / Timing ----------

  static double _parseTime(String ts) {
    // "mm:ss:ms" => seconds (double)
    final parts = ts.split(":");
    if (parts.length != 3) return 0.0;
    return int.parse(parts[0]) * 60 + int.parse(parts[1]) + (int.parse(parts[2]) / 1000.0);
  }

  static String _fmt(double v) => v.toStringAsFixed(3);

  static List<String> _wrap(String text, {int maxChars = 25}) {
    final words = text.split(RegExp(r'\s+')).where((w) => w.isNotEmpty).toList();
    final lines = <String>[];
    var current = "";
    for (final w in words) {
      if (current.isEmpty) {
        current = w;
      } else if ((current.length + 1 + w.length) <= maxChars) {
        current = "$current $w";
      } else {
        lines.add(current);
        current = w;
      }
    }
    if (current.isNotEmpty) lines.add(current);
    return lines;
  }

  static List<_LyricImageSpec> _buildLyricSpecs({
    required List<dynamic> lyricsData,
    required double durationSeconds,
  }) {
    final specs = <_LyricImageSpec>[];
    if (lyricsData.isEmpty) return specs;

    // preroll (before first line becomes current), show first line faded (same as your code)
    final first = lyricsData.first;
    final firstStart = _parseTime(first["start_time"] ?? "0:0:0");
    if (firstStart > 0) {
      final currentLines = _wrap(first["text"] ?? "", maxChars: 20);
      specs.add(_LyricImageSpec(
        start: 0.0,
        end: firstStart,
        currentLines: currentLines,
        // for preroll, mark it as faded-current (we'll draw current using 'other' color)
        prerollFaded: true,
        prevLines: const [],
        nextLines: _wrap(lyricsData.length > 1 ? (lyricsData[1]["text"] ?? "") : "", maxChars: 25),
      ));
    }

    for (int i = 0; i < lyricsData.length; i++) {
      final item = lyricsData[i];
      final start = _parseTime(item["start_time"] ?? "0:0:0");
      double end = _parseTime(item["end_time"] ?? "0:0:0");
      if (end == 0.0 && i == lyricsData.length - 1) {
        end = durationSeconds;
      }
      final currentLines = _wrap(item["text"] ?? "", maxChars: 20);
      final prevLines = i > 0 ? _wrap(lyricsData[i - 1]["text"] ?? "", maxChars: 25) : <String>[];
      final nextLines = i < lyricsData.length - 1 ? _wrap(lyricsData[i + 1]["text"] ?? "", maxChars: 25) : <String>[];

      specs.add(_LyricImageSpec(
        start: start,
        end: math.max(start, end),
        currentLines: currentLines,
        prevLines: prevLines,
        nextLines: nextLines,
      ));
    }

    return specs;
  }

  // ---------- Canvas Rendering ----------

  static Future<void> _renderMetadataOverlay({
    required String outPath,
    required String trackName,
    required String originalArtist,
    required String username,
    required String familyMain,
    required String familyMeta,
    required String colorHex,
    required double textX,
    required double textY,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder, ui.Rect.fromLTWH(0, 0, BG_W.toDouble(), BG_H.toDouble()));
    // Transparent background
    final bgPaint = ui.Paint()..color = const ui.Color(0x00000000);
    canvas.drawRect(ui.Rect.fromLTWH(0, 0, BG_W.toDouble(), BG_H.toDouble()), bgPaint);

    final metaColor = _parseColor(colorHex);

    // trackName (Main font, 40)
    await _drawText(
      canvas: canvas,
      text: trackName,
      family: familyMain,
      size: 40,
      color: metaColor,
      // Draw at absolute position, left-aligned to match original x=TEXT_X
      // We’ll place by baseline using top-left point like FFmpeg
      offset: ui.Offset(textX, textY),
      centerHorizontally: false,
    );

    // originalArtist (Meta font, 32)
    await _drawText(
      canvas: canvas,
      text: originalArtist,
      family: familyMeta,
      size: 32,
      color: metaColor,
      offset: ui.Offset(textX, textY + 50),
      centerHorizontally: false,
    );

    // username (Meta font, 36)
    await _drawText(
      canvas: canvas,
      text: username,
      family: familyMeta,
      size: 36,
      color: metaColor,
      offset: ui.Offset(textX, textY + 140),
      centerHorizontally: false,
    );

    final picture = recorder.endRecording();
    final img = await picture.toImage(BG_W, BG_H);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();
    await File(outPath).writeAsBytes(pngBytes, flush: true);
  }

  static Future<void> _renderLyricStateImage({
    required String outPath,
    required _LyricImageSpec spec,
    required String familyMain,
    required String colorCurrentHex,
    required String colorOtherHex,
    required int lyricsStartY,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = ui.Canvas(recorder, ui.Rect.fromLTWH(0, 0, BG_W.toDouble(), BG_H.toDouble()));

    // Transparent background
    final bgPaint = ui.Paint()..color = const ui.Color(0x00000000);
    canvas.drawRect(ui.Rect.fromLTWH(0, 0, BG_W.toDouble(), BG_H.toDouble()), bgPaint);

    final colorCurrent = _parseColor(colorCurrentHex);
    final colorOther = _parseColor(colorOtherHex);

    // PREVIOUS (top area)
    for (int j = 0; j < spec.prevLines.length; j++) {
      final line = spec.prevLines[j];
      final y = lyricsStartY + j * lineHeightOther;
      await _drawText(
        canvas: canvas,
        text: line,
        family: familyMain,
        size: fontSizeOther.toDouble(),
        color: colorOther,
        offset: ui.Offset(BG_W / 2, y.toDouble()),
        centerHorizontally: true,
      );
    }

    // CURRENT (middle block)
    for (int j = 0; j < spec.currentLines.length; j++) {
      final line = spec.currentLines[j];
      final y = lyricsStartY + blockHeight + j * lineHeightCurrent;
      final color = spec.prerollFaded ? colorOther : colorCurrent;
      await _drawText(
        canvas: canvas,
        text: line,
        family: familyMain,
        size: fontSizeCurrent.toDouble(),
        color: color,
        offset: ui.Offset(BG_W / 2, y.toDouble()),
        centerHorizontally: true,
      );
    }

    // NEXT (bottom area, roughly +280 from start)
    for (int j = 0; j < spec.nextLines.length; j++) {
      final line = spec.nextLines[j];
      final y = lyricsStartY + 2 * 140 + j * lineHeightOther;
      await _drawText(
        canvas: canvas,
        text: line,
        family: familyMain,
        size: fontSizeOther.toDouble(),
        color: colorOther,
        offset: ui.Offset(BG_W / 2, y.toDouble()),
        centerHorizontally: true,
      );
    }

    final picture = recorder.endRecording();
    final img = await picture.toImage(BG_W, BG_H);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final pngBytes = byteData!.buffer.asUint8List();
    await File(outPath).writeAsBytes(pngBytes, flush: true);
  }

  static Future<void> _drawText({
    required ui.Canvas canvas,
    required String text,
    required String family,
    required double size,
    required ui.Color color,
    required ui.Offset offset,
    required bool centerHorizontally,
  }) async {
    final tp = ui.ParagraphBuilder(
      ui.ParagraphStyle(
        fontFamily: family,
        fontSize: size,
        textAlign: centerHorizontally ? ui.TextAlign.center : ui.TextAlign.left,
        maxLines: 5,
      ),
    )
      ..pushStyle(ui.TextStyle(color: color))
      ..addText(text);

    final paragraph = tp.build()..layout(ui.ParagraphConstraints(width: centerHorizontally ? BG_W.toDouble() : (BG_W - offset.dx)));

    double dx = offset.dx;
    if (centerHorizontally) {
      // We laid out with full width for center; draw with dx=0 and y=offset.y
      dx = 0;
    }
    canvas.drawParagraph(paragraph, ui.Offset(dx, offset.dy));
  }

  static ui.Color _parseColor(String hex) {
    // Accept #AARRGGBB or #RRGGBBAA; your inputs are #AARRGGBB
    String h = hex.replaceAll("#", "");
    if (h.length == 8) {
      final a = int.parse(h.substring(0, 2), radix: 16);
      final r = int.parse(h.substring(2, 4), radix: 16);
      final g = int.parse(h.substring(4, 6), radix: 16);
      final b = int.parse(h.substring(6, 8), radix: 16);
      return ui.Color.fromARGB(a, r, g, b);
    }
    if (h.length == 6) {
      final r = int.parse(h.substring(0, 2), radix: 16);
      final g = int.parse(h.substring(2, 4), radix: 16);
      final b = int.parse(h.substring(4, 6), radix: 16);
      return ui.Color.fromARGB(0xFF, r, g, b);
    }
    return const ui.Color(0xFFFFFFFF);
  }

  // ---------- Font loading from file paths ----------

  static Future<void> _registerFontFamilyFromFile(String family, String path) async {
    final loader = FontLoader(family);
    final bytes = await File(path).readAsBytes();
    final byteData = bytes.buffer.asByteData();
    loader.addFont(Future.value(byteData));
    await loader.load();
  }

  static String _shortHash(String s) {
    // tiny non-crypto hash for family uniqueness
    int h = 0;
    for (int i = 0; i < s.length; i++) {
      h = 0x1fffffff & (h + s.codeUnitAt(i));
      h = 0x1fffffff & (h + ((0x0007ffff & h) << 10));
      h ^= (h >> 6);
    }
    h = 0x1fffffff & (h + ((0x03ffffff & h) << 3));
    h ^= (h >> 11);
    h = 0x1fffffff & (h + ((0x00003fff & h) << 15));
    return h.toRadixString(36);
  }

  // ---------- Files / Duration ----------

  static Future<Directory> _prepareTempDir() async {
    final dir = Directory("${Directory.systemTemp.path}/melodyze_text_${DateTime.now().millisecondsSinceEpoch}");
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    return dir;
  }

  static Future<double> _getAudioDuration(String audioPath) async {
    // Quick probe through ffmpeg itself (fallback if you don’t use a native probe)
    final cmd = "-i ${_q(audioPath)} -hide_banner";
    final session = await FFmpegKit.execute(cmd);
    final logs = await session.getAllLogsAsString() ?? "";
    final match = RegExp(r"Duration:\s*(\d+):(\d+):(\d+\.\d+)").firstMatch(logs);
    if (match != null) {
      final h = int.parse(match.group(1)!);
      final m = int.parse(match.group(2)!);
      final s = double.parse(match.group(3)!);
      return h * 3600 + m * 60 + s;
    }
    // Fallback if probe fails: assume 180s to avoid crash (or throw)
    return 180.0;
  }

  static String _q(String s) {
    if (s.contains("'")) return "\"$s\"";
    return "'$s'";
  }
}

// ---------- Model for a lyric-state image ----------

class _LyricImageSpec {
  final double start;
  final double end;
  final List<String> prevLines;
  final List<String> currentLines;
  final List<String> nextLines;
  final bool prerollFaded;

  _LyricImageSpec({
    required this.start,
    required this.end,
    required this.currentLines,
    required this.prevLines,
    required this.nextLines,
    this.prerollFaded = false,
  });
}
